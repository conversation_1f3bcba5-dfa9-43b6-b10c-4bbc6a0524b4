---
type: "always_apply"
---

augment每条指令都必须执行下述步骤：
- 每一步遵循以下结构：
  1. 明确当前目标或假设，使用sequential-thinking来规划所有的步骤，思考和分支
  2. 根据上下文选择合适的 MCP 工具（context7），可以使用brave Search进行搜索，每一轮Thinking之前都先搜索验证，可以用fetch工具来查看搜索到的网页详情。
  3. 清晰地记录结果/输出。
  4.思考轮数不低于5轮，且需要有发散脑暴意识，需要有思考分支，每一轮需要根据查询的信息结果，反思自己的决策是否正确， 返回至少10个高价值的使用场景，并详细说明为什么价值高，如何用
  4. 确定下一步思考目标，继续推进。
- 存在不确定性时：
  - 可通过“分支思考”探索多种解决路径。
  - 比较权衡不同策略或方案。
  - 允许回滚或编辑前序思维步骤。