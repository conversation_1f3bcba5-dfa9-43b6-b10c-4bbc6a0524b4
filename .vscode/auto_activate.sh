#!/bin/bash

# 自动激活ml_env_new环境并设置环境变量
source /home/<USER>/miniforge_25.3.0/bin/activate ml_env_new

# 设置环境变量
export TRAIN_DATA_PATH="./TencentGR_1k"
export TRAIN_LOG_PATH="./logs"
export TRAIN_TF_EVENTS_PATH="./logs/tf_events"
export TRAIN_CKPT_PATH="./logs/ckpt"

# 创建必要的目录
mkdir -p "$TRAIN_LOG_PATH" "$TRAIN_TF_EVENTS_PATH" "$TRAIN_CKPT_PATH"

# 显示环境信息
echo "🎯 环境已自动激活："
echo "   Python: $(which python)"
echo "   版本: $(python --version)"
echo "   环境: $CONDA_DEFAULT_ENV"
echo "   包数量: $(pip list | wc -l)"
echo "   GPU可用: $(python -c 'import torch; print(torch.cuda.is_available())')"
echo ""
echo "✅ 环境配置完成，可以直接开始工作！"
