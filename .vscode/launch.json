{"version": "0.2.0", "configurations": [{"name": "Python: Current File", "type": "python", "request": "launch", "program": "${file}", "console": "integratedTerminal", "python": "/home/<USER>/miniforge_25.3.0/envs/ml_env_new/bin/python", "env": {"TRAIN_DATA_PATH": "./TencentGR_1k", "TRAIN_LOG_PATH": "./logs", "TRAIN_TF_EVENTS_PATH": "./logs/tf_events", "TRAIN_CKPT_PATH": "./logs/ckpt"}}, {"name": "Python: main.py", "type": "python", "request": "launch", "program": "${workspaceFolder}/main.py", "console": "integratedTerminal", "python": "/home/<USER>/miniforge_25.3.0/envs/ml_env_new/bin/python", "args": ["--num_epochs", "1", "--batch_size", "32"], "env": {"TRAIN_DATA_PATH": "./TencentGR_1k", "TRAIN_LOG_PATH": "./logs", "TRAIN_TF_EVENTS_PATH": "./logs/tf_events", "TRAIN_CKPT_PATH": "./logs/ckpt"}}]}