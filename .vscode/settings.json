{"claude.enableAllProjectMcpServers": true, "claude.enabledMcpjsonServers": ["desktop-commander", "context7", "sequential-thinking"], "python.defaultInterpreter": "/home/<USER>/miniforge_25.3.0/envs/ml_env_new/bin/python", "python.terminal.activateEnvironment": true, "python.terminal.activateEnvInCurrentTerminal": true, "terminal.integrated.defaultProfile.linux": "ML Environment", "terminal.integrated.profiles.linux": {"ML Environment": {"path": "bash", "args": ["-c", "source /home/<USER>/miniforge_25.3.0/bin/activate ml_env_new && export TRAIN_DATA_PATH='./TencentGR_1k' && export TRAIN_LOG_PATH='./logs' && export TRAIN_TF_EVENTS_PATH='./logs/tf_events' && export TRAIN_CKPT_PATH='./logs/ckpt' && mkdir -p ./logs ./logs/tf_events ./logs/ckpt && echo '🎯 环境已自动激活: ml_env_new (200包) GPU可用' && exec bash"]}, "bash": {"path": "bash"}}, "terminal.integrated.env.linux": {"TRAIN_DATA_PATH": "./TencentGR_1k", "TRAIN_LOG_PATH": "./logs", "TRAIN_TF_EVENTS_PATH": "./logs/tf_events", "TRAIN_CKPT_PATH": "./logs/ckpt"}}